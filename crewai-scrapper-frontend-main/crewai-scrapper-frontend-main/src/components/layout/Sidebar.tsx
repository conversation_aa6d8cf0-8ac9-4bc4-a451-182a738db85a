import React, { useState, useEffect } from "react";
import { Home, Heart, PanelLeftClose, History, Trash } from "lucide-react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { RootState } from "../../api/store";
import AuthModal from "../common/AuthModal";
import {
  useGetSearchHistoryQuery,
  useDeleteSearchHistoryMutation,
  useClearSearchHistoryMutation,
} from "../../api/services/History/HistoryService.ts";

interface SearchHistoryItem {
  id?: string; // only present for server-side history
  term: string;
  timestamp: number;
}

interface ServerHistoryItem {
  id: string;
  term: string;
  createdAt: string;
  user;
}

const Sidebar: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [showHistory, setShowHistory] = useState(false); // default true to show
  const [selectedHistoryProduct, setSelectedHistoryProduct] = useState<
    number | null
  >(null);
  const [step, setStep] = useState<
    "register" | "otp" | "login" | "forget-password" | "reset-password"
  >("login");
  const [searchHistory, setSearchHistory] = useState<SearchHistoryItem[]>([]);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  const { isAuthenticated, user } = useSelector(
    (state: RootState) => state.auth
  );

  const {
    data: serverHistoryResponse,
    isSuccess,
    refetch,
  } = useGetSearchHistoryQuery(undefined, {
    skip: !isAuthenticated,
  });

  const [deleteSearchHistory] = useDeleteSearchHistoryMutation();
  const [clearSearchHistory] = useClearSearchHistoryMutation();

  useEffect(() => {
    if (!location.pathname.startsWith("/products")) {
      setSelectedHistoryProduct(null);
    }
  }, [location]);

  // Load local or server history
  useEffect(() => {
    if (!isAuthenticated) {
      const saved = localStorage.getItem("searchHistory");
      if (saved) {
        try {
          const parsed = JSON.parse(saved);
          setSearchHistory(parsed);
        } catch (err) {
          setSearchHistory([]);
        }
      } else {
        setSearchHistory([]);
      }
    } else if (isSuccess && serverHistoryResponse?.data?.history) {
      const transformedHistory: SearchHistoryItem[] =
        serverHistoryResponse.data.history.map((item: ServerHistoryItem) => ({
          id: item.id,
          term: item.term,
          timestamp: new Date(item.createdAt).getTime(),
        }));
      setSearchHistory(transformedHistory);
    }
  }, [isAuthenticated, isSuccess, serverHistoryResponse]);

  const toggleSidebar = () => {
    setCollapsed(!collapsed);
    setShowHistory(false);
  };

  const toggleHistory = () => {
    if (collapsed) setCollapsed(false);
    setShowHistory(!showHistory);
  };

  const getInitials = (name: string) =>
    name
      .trim()
      .split(" ")
      .slice(0, 2)
      .map((n) => n[0])
      .join("")
      .toUpperCase();

  const removeHistoryItem = async (index: number) => {
    const item = searchHistory[index];
    if (!isAuthenticated) {
      const updated = searchHistory.filter((_, i) => i !== index);
      setSearchHistory(updated);
      localStorage.setItem("searchHistory", JSON.stringify(updated));
      window.dispatchEvent(new Event("searchHistoryUpdate"));
    } else if (item?.id) {
      try {
        await deleteSearchHistory(item.id).unwrap();
        refetch();
      } catch (err) {
        console.error("Failed to delete search history", err);
      }
    }
  };

  const handleClearHistory = async () => {
    if (!isAuthenticated) {
      localStorage.removeItem("searchHistory");
      setSearchHistory([]);
      window.dispatchEvent(new Event("searchHistoryUpdate"));
    } else {
      try {
        await clearSearchHistory(undefined).unwrap();
        refetch();
      } catch (err) {
        console.error("Failed to clear search history", err);
      }
    }
  };

  const handleFavourites = () => {
    if (!isAuthenticated) {
      setShowAuthModal(true);
    } else {
      navigate("/favorites");
      setShowHistory(false);
    }
  };

  return (
    <div
      className={`bg-white shadow-md transition-all duration-300 flex flex-col ${
        collapsed ? "w-16" : "w-64"
      } min-h-screen relative`}
    >
      {/* Header */}
      <div
        className={`flex ${
          collapsed
            ? "flex-col items-center space-y-2 p-2"
            : "items-center justify-between pl-2 p-5"
        }`}
      >
        {collapsed ? (
          <Link to="/home">
            <img
              src="/assets/small-logo.png"
              alt="Small Logo"
              className="h-8 w-auto"
            />
          </Link>
        ) : (
          <Link to="/home" className="w-full">
            <img
              src="/assets/large-logo.svg"
              alt="Large Logo"
              className="h-7 w-auto pl-4"
            />
          </Link>
        )}

        <button
          className={`p-2 rounded-lg hover:bg-gray-100 transition ${
            collapsed ? "" : "ml-8"
          }`}
          onClick={toggleSidebar}
        >
          <PanelLeftClose
            className={`w-5 h-5 text-gray-700 transition-transform duration-300 ${
              collapsed ? "rotate-180" : ""
            }`}
          />
        </button>
      </div>

      {/* Navigation */}
      <nav className="space-y-2 px-2">
        <Link
          to="/home"
          onClick={() => setShowHistory(false)}
          className={`flex items-center rounded-lg px-3 py-2 hover:bg-pink-100 transition group ${
            collapsed ? "justify-center" : ""
          }`}
        >
          <Home className="w-5 h-5 text-gray-700 group-hover:text-pink-600" />
          {!collapsed && (
            <span className="ml-3 text-gray-700 font-medium">Home</span>
          )}
        </Link>

        <div
          onClick={handleFavourites}
          className={`cursor-pointer flex items-center rounded-lg px-3 py-2 hover:bg-pink-100 transition group ${
            collapsed ? "justify-center" : ""
          }`}
        >
          <Heart className="w-5 h-5 text-gray-700 group-hover:text-pink-600" />
          {!collapsed && (
            <span className="ml-3 text-gray-700 font-medium">Favorites</span>
          )}
        </div>

        {searchHistory.length > 0 && (
          <button
            onClick={toggleHistory}
            className={`w-full flex items-center rounded-lg px-3 py-2 hover:bg-pink-100 transition group ${
              collapsed ? "justify-center" : ""
            }`}
          >
            <History className="w-5 h-5 text-gray-700 group-hover:text-pink-600" />
            {!collapsed && (
              <span className="ml-3 text-gray-700 font-medium">History</span>
            )}
          </button>
        )}
      </nav>

      {/* History List */}
      {searchHistory.length > 0 && showHistory && !collapsed && (
        <div className="mx-2 mt-2 rounded-lg h-full overflow-y-auto mb-5">
          <div className="space-y-1 px-3">
            {[...searchHistory]
              .sort((a, b) => b.timestamp - a.timestamp)
              .map((item, index) => (
                <div
                  key={`${item.term}-${item.timestamp}-${index}`}
                  className={`flex items-center justify-between p-2 rounded transition cursor-pointer ${
                    selectedHistoryProduct === index
                      ? "shadow-md bg-blue-100/50"
                      : "hover:bg-blue-100/35 hover:shadow-md"
                  }`}
                  onClick={() => {
                    setSelectedHistoryProduct(index);
                    navigate(
                      `/products?search=${encodeURIComponent(item.term)}`
                    );
                  }}
                >
                  <button className="w-full text-sm text-left text-gray-700 font-medium truncate focus:outline-none">
                    {item.term}
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      removeHistoryItem(index);
                    }}
                    className="p-1 rounded transition"
                  >
                    <Trash className="w-4 h-4 hover:text-pink-500 text-gray-700" />
                  </button>
                </div>
              ))}
            <div className="flex justify-end mt-1">
              <button
                onClick={handleClearHistory}
                className="text-xs text-red-500 hover:text-red-600 underline"
              >
                Clear All
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Footer (User Info) */}
      {isAuthenticated && user?.name && (
        <div className="mt-auto px-4 py-5 flex items-center justify-start border-t">
          <div className="bg-pink-100 text-pink-600 font-bold rounded-full w-9 h-9 flex items-center justify-center">
            {getInitials(user.name)}
          </div>
          {!collapsed && (
            <span className="text-gray-700 font-medium text-sm whitespace-nowrap ml-2">
              {user.name.split(" ").slice(0, 2).join(" ") +
                (user.name.split(" ").length > 2 ? "..." : "")}
            </span>
          )}
        </div>
      )}

      {/* Auth Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => {
          setShowAuthModal(false);
          setStep("login");
        }}
        step={step}
        setStep={setStep}
      />
    </div>
  );
};

export default Sidebar;
