import { StatusCodes } from "http-status-codes";
import { getConversationRepo, getMessageRepo } from "../../../models/chat/index.js";
import { getUserRepo } from "../../../models/user/index.js";
import { 
  generateSearchTermFromConversation, 
  generateConversationTitle, 
  generateContextualResponse 
} from "../../third-party/OpenAI/index.js";
import { BaseAPIResponse } from "../../utils/responses/index.js";
import { CustomApiError } from "../../utils/errors/index.js";
import { CustomLogger } from "../../utils/logger/index.js";

// Create a new conversation
export const createConversation = async (req, res) => {
  try {
    const { initialMessage } = req.body;
    const userId = req.user.id;

    if (!initialMessage?.trim()) {
      return res.status(StatusCodes.BAD_REQUEST).json(
        new CustomApiError(StatusCodes.BAD_REQUEST, "Initial message is required")
      );
    }

    const conversationRepo = getConversationRepo();
    const messageRepo = getMessageRepo();

    // Generate AI response and search term
    const aiResult = await generateSearchTermFromConversation([], initialMessage);
    const title = await generateConversationTitle(initialMessage);

    // Create conversation
    const conversation = conversationRepo.create({
      user: { id: userId },
      title,
      initialSearchTerm: aiResult.searchTerm,
      lastSearchTerm: aiResult.searchTerm,
      context: {
        productPreferences: [],
        searchHistory: [aiResult.searchTerm]
      }
    });

    const savedConversation = await conversationRepo.save(conversation);

    // Create initial user message
    const userMessage = messageRepo.create({
      conversation: { id: savedConversation.id },
      content: initialMessage,
      role: "user"
    });

    // Create AI response message
    const aiMessage = messageRepo.create({
      conversation: { id: savedConversation.id },
      content: aiResult.response,
      role: "assistant",
      searchTermGenerated: aiResult.searchTerm,
      metadata: {
        searchTerm: aiResult.searchTerm,
        timestamp: new Date().toISOString()
      }
    });

    await messageRepo.save([userMessage, aiMessage]);

    return res.status(StatusCodes.CREATED).json(
      new BaseAPIResponse({
        statusCode: StatusCodes.CREATED,
        message: "Conversation created successfully",
        data: {
          conversation: savedConversation,
          searchTerm: aiResult.searchTerm,
          messages: [userMessage, aiMessage]
        }
      })
    );
  } catch (error) {
    CustomLogger.error("Error creating conversation:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json(
      new CustomApiError(StatusCodes.INTERNAL_SERVER_ERROR, "Failed to create conversation")
    );
  }
};

// Add message to existing conversation
export const addMessage = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { message } = req.body;
    const userId = req.user.id;

    if (!message?.trim()) {
      return res.status(StatusCodes.BAD_REQUEST).json(
        new CustomApiError(StatusCodes.BAD_REQUEST, "Message is required")
      );
    }

    const conversationRepo = getConversationRepo();
    const messageRepo = getMessageRepo();

    // Get conversation with messages
    const conversation = await conversationRepo.findOne({
      where: { id: conversationId, user: { id: userId } },
      relations: ["messages"]
    });

    if (!conversation) {
      return res.status(StatusCodes.NOT_FOUND).json(
        new CustomApiError(StatusCodes.NOT_FOUND, "Conversation not found")
      );
    }

    // Get recent messages for context
    const recentMessages = conversation.messages
      .sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt))
      .slice(-10);

    // Generate AI response and search term
    const aiResult = await generateSearchTermFromConversation(recentMessages, message);

    // Create user message
    const userMessage = messageRepo.create({
      conversation: { id: conversationId },
      content: message,
      role: "user"
    });

    // Create AI response message
    const aiMessage = messageRepo.create({
      conversation: { id: conversationId },
      content: aiResult.response,
      role: "assistant",
      searchTermGenerated: aiResult.searchTerm,
      metadata: {
        searchTerm: aiResult.searchTerm,
        timestamp: new Date().toISOString()
      }
    });

    await messageRepo.save([userMessage, aiMessage]);

    // Update conversation context
    const updatedContext = {
      ...conversation.context,
      searchHistory: [
        ...(conversation.context?.searchHistory || []),
        aiResult.searchTerm
      ].slice(-10) // Keep last 10 search terms
    };

    await conversationRepo.update(conversationId, {
      lastSearchTerm: aiResult.searchTerm,
      context: updatedContext,
      updatedAt: new Date()
    });

    return res.status(StatusCodes.OK).json(
      new BaseAPIResponse({
        statusCode: StatusCodes.OK,
        message: "Message added successfully",
        data: {
          searchTerm: aiResult.searchTerm,
          messages: [userMessage, aiMessage]
        }
      })
    );
  } catch (error) {
    CustomLogger.error("Error adding message:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json(
      new CustomApiError(StatusCodes.INTERNAL_SERVER_ERROR, "Failed to add message")
    );
  }
};

// Get conversation with messages
export const getConversation = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const userId = req.user.id;

    const conversationRepo = getConversationRepo();

    const conversation = await conversationRepo.findOne({
      where: { id: conversationId, user: { id: userId } },
      relations: ["messages"],
      order: {
        messages: {
          createdAt: "ASC"
        }
      }
    });

    if (!conversation) {
      return res.status(StatusCodes.NOT_FOUND).json(
        new CustomApiError(StatusCodes.NOT_FOUND, "Conversation not found")
      );
    }

    return res.status(StatusCodes.OK).json(
      new BaseAPIResponse({
        statusCode: StatusCodes.OK,
        message: "Conversation retrieved successfully",
        data: conversation
      })
    );
  } catch (error) {
    CustomLogger.error("Error getting conversation:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json(
      new CustomApiError(StatusCodes.INTERNAL_SERVER_ERROR, "Failed to get conversation")
    );
  }
};

// Get user's conversations
export const getUserConversations = async (req, res) => {
  try {
    const userId = req.user.id;
    const { page = 1, limit = 10 } = req.query;

    const conversationRepo = getConversationRepo();

    const [conversations, total] = await conversationRepo.findAndCount({
      where: { user: { id: userId }, isActive: true },
      order: { updatedAt: "DESC" },
      skip: (page - 1) * limit,
      take: limit,
      relations: ["messages"]
    });

    // Add message count and last message to each conversation
    const conversationsWithMeta = conversations.map(conv => ({
      ...conv,
      messageCount: conv.messages?.length || 0,
      lastMessage: conv.messages?.slice(-1)[0] || null,
      messages: undefined // Remove messages from response to keep it light
    }));

    return res.status(StatusCodes.OK).json(
      new BaseAPIResponse({
        statusCode: StatusCodes.OK,
        message: "Conversations retrieved successfully",
        data: {
          conversations: conversationsWithMeta,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / limit)
          }
        }
      })
    );
  } catch (error) {
    CustomLogger.error("Error getting user conversations:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json(
      new CustomApiError(StatusCodes.INTERNAL_SERVER_ERROR, "Failed to get conversations")
    );
  }
};

// Delete conversation
export const deleteConversation = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const userId = req.user.id;

    const conversationRepo = getConversationRepo();

    const conversation = await conversationRepo.findOne({
      where: { id: conversationId, user: { id: userId } }
    });

    if (!conversation) {
      return res.status(StatusCodes.NOT_FOUND).json(
        new CustomApiError(StatusCodes.NOT_FOUND, "Conversation not found")
      );
    }

    await conversationRepo.update(conversationId, { isActive: false });

    return res.status(StatusCodes.OK).json(
      new BaseAPIResponse({
        statusCode: StatusCodes.OK,
        message: "Conversation deleted successfully"
      })
    );
  } catch (error) {
    CustomLogger.error("Error deleting conversation:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json(
      new CustomApiError(StatusCodes.INTERNAL_SERVER_ERROR, "Failed to delete conversation")
    );
  }
};
